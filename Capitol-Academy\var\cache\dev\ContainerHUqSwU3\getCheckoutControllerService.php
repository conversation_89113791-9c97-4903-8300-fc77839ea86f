<?php

namespace ContainerHUqSwU3;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getCheckoutControllerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the public 'App\Controller\CheckoutController' shared autowired service.
     *
     * @return \App\Controller\CheckoutController
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'framework-bundle'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AbstractController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'CheckoutController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'PayPalService.php';

        $a = ($container->privates['monolog.logger'] ?? self::getMonolog_LoggerService($container));

        $container->services['App\\Controller\\CheckoutController'] = $instance = new \App\Controller\CheckoutController(($container->privates['App\\Service\\CartService'] ?? $container->load('getCartServiceService')), new \App\Service\PayPalService(($container->privates['.debug.http_client'] ?? self::get_Debug_HttpClientService($container)), $a, ($container->privates['parameter_bag'] ??= new \Symfony\Component\DependencyInjection\ParameterBag\ContainerBag($container))), ($container->privates['App\\Service\\AccessControlService'] ?? $container->load('getAccessControlServiceService')), ($container->privates['App\\Service\\CourseEnrollmentService'] ?? $container->load('getCourseEnrollmentServiceService')), ($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), $a);

        $instance->setContainer(($container->privates['.service_locator.O2p6Lk7'] ?? $container->load('get_ServiceLocator_O2p6Lk7Service'))->withContext('App\\Controller\\CheckoutController', $container));

        return $instance;
    }
}
