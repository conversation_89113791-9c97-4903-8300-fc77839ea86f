<?php

namespace ContainerHUqSwU3;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_J7GYip_Service extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.j7GYip.' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.j7GYip.'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'App\\Controller\\AdminController::addAdmin' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AdminController::blockEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::blockUser' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController::certifyEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::createEnrollment' => ['privates', '.service_locator.ubULoiL', 'get_ServiceLocator_UbULoiLService', true],
            'App\\Controller\\AdminController::dashboard' => ['privates', '.service_locator.e5Iizj.', 'get_ServiceLocator_E5Iizj_Service', true],
            'App\\Controller\\AdminController::deleteAdmin' => ['privates', '.service_locator.Qyz7DBH', 'get_ServiceLocator_Qyz7DBHService', true],
            'App\\Controller\\AdminController::deleteCourse' => ['privates', '.service_locator.jqHm011', 'get_ServiceLocator_JqHm011Service', true],
            'App\\Controller\\AdminController::deleteEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::deletePartner' => ['privates', '.service_locator.YuEbyXL', 'get_ServiceLocator_YuEbyXLService', true],
            'App\\Controller\\AdminController::deletePromotionalBanner' => ['privates', '.service_locator.kleoBdA', 'get_ServiceLocator_KleoBdAService', true],
            'App\\Controller\\AdminController::deleteUser' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController::editAdmin' => ['privates', '.service_locator.mCbvPMW', 'get_ServiceLocator_MCbvPMWService', true],
            'App\\Controller\\AdminController::editEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::editPartner' => ['privates', '.service_locator.YuEbyXL', 'get_ServiceLocator_YuEbyXLService', true],
            'App\\Controller\\AdminController::editPromotionalBanner' => ['privates', '.service_locator.kleoBdA', 'get_ServiceLocator_KleoBdAService', true],
            'App\\Controller\\AdminController::enrollmentDetails' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::enrollmentDetailsByCode' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::enrollmentsList' => ['privates', '.service_locator.e5Iizj.', 'get_ServiceLocator_E5Iizj_Service', true],
            'App\\Controller\\AdminController::listAdmins' => ['privates', '.service_locator.Qyz7DBH', 'get_ServiceLocator_Qyz7DBHService', true],
            'App\\Controller\\AdminController::markEnrollmentCompleted' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::profile' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AdminController::showPartner' => ['privates', '.service_locator.YuEbyXL', 'get_ServiceLocator_YuEbyXLService', true],
            'App\\Controller\\AdminController::testEmail' => ['privates', '.service_locator.uVvF4gL', 'get_ServiceLocator_UVvF4gLService', true],
            'App\\Controller\\AdminController::toggleAdminStatus' => ['privates', '.service_locator.Qyz7DBH', 'get_ServiceLocator_Qyz7DBHService', true],
            'App\\Controller\\AdminController::toggleCourseStatus' => ['privates', '.service_locator.jqHm011', 'get_ServiceLocator_JqHm011Service', true],
            'App\\Controller\\AdminController::togglePartner' => ['privates', '.service_locator.YuEbyXL', 'get_ServiceLocator_YuEbyXLService', true],
            'App\\Controller\\AdminController::togglePromotionalBannerStatus' => ['privates', '.service_locator.kleoBdA', 'get_ServiceLocator_KleoBdAService', true],
            'App\\Controller\\AdminController::toggleUserStatus' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController::unblockEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::unblockUser' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController::updateEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::updateEnrollmentProgress' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController::userEdit' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController::userShow' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController::viewAdmin' => ['privates', '.service_locator.mCbvPMW', 'get_ServiceLocator_MCbvPMWService', true],
            'App\\Controller\\AdminInstructorController::delete' => ['privates', '.service_locator.8RUuq9s', 'get_ServiceLocator_8RUuq9sService', true],
            'App\\Controller\\AdminInstructorController::edit' => ['privates', '.service_locator.c34Bris', 'get_ServiceLocator_C34BrisService', true],
            'App\\Controller\\AdminInstructorController::index' => ['privates', '.service_locator.L29W5HS', 'get_ServiceLocator_L29W5HSService', true],
            'App\\Controller\\AdminInstructorController::new' => ['privates', '.service_locator.c34Bris', 'get_ServiceLocator_C34BrisService', true],
            'App\\Controller\\AdminInstructorController::print' => ['privates', '.service_locator.4qavBNK', 'get_ServiceLocator_4qavBNKService', true],
            'App\\Controller\\AdminInstructorController::reorder' => ['privates', '.service_locator.L29W5HS', 'get_ServiceLocator_L29W5HSService', true],
            'App\\Controller\\AdminInstructorController::show' => ['privates', '.service_locator.L29W5HS', 'get_ServiceLocator_L29W5HSService', true],
            'App\\Controller\\AdminInstructorController::toggleStatus' => ['privates', '.service_locator.8RUuq9s', 'get_ServiceLocator_8RUuq9sService', true],
            'App\\Controller\\AdminMarketAnalysisController::delete' => ['privates', '.service_locator.WGDSHn2', 'get_ServiceLocator_WGDSHn2Service', true],
            'App\\Controller\\AdminMarketAnalysisController::edit' => ['privates', '.service_locator.WGDSHn2', 'get_ServiceLocator_WGDSHn2Service', true],
            'App\\Controller\\AdminMarketAnalysisController::show' => ['privates', '.service_locator.WGDSHn2', 'get_ServiceLocator_WGDSHn2Service', true],
            'App\\Controller\\AdminMarketAnalysisController::toggleStatus' => ['privates', '.service_locator.WGDSHn2', 'get_ServiceLocator_WGDSHn2Service', true],
            'App\\Controller\\AdminSecurityController::createAdmin' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AdminSecurityController::login' => ['privates', '.service_locator.rSTd.nA', 'get_ServiceLocator_RSTd_NAService', true],
            'App\\Controller\\Admin\\CategoryController::show' => ['privates', '.service_locator.8IESKP1', 'get_ServiceLocator_8IESKP1Service', true],
            'App\\Controller\\Admin\\CategoryController::toggleCourses' => ['privates', '.service_locator.8IESKP1', 'get_ServiceLocator_8IESKP1Service', true],
            'App\\Controller\\Admin\\CategoryController::toggleStatus' => ['privates', '.service_locator.8IESKP1', 'get_ServiceLocator_8IESKP1Service', true],
            'App\\Controller\\Admin\\CategoryController::toggleVideos' => ['privates', '.service_locator.8IESKP1', 'get_ServiceLocator_8IESKP1Service', true],
            'App\\Controller\\Admin\\DashboardController::index' => ['privates', '.service_locator..XI94lU', 'get_ServiceLocator__XI94lUService', true],
            'App\\Controller\\Admin\\OrderController::export' => ['privates', '.service_locator.4n4ylFv', 'get_ServiceLocator_4n4ylFvService', true],
            'App\\Controller\\Admin\\OrderController::index' => ['privates', '.service_locator.4n4ylFv', 'get_ServiceLocator_4n4ylFvService', true],
            'App\\Controller\\Admin\\OrderController::refund' => ['privates', '.service_locator.ruxHxcA', 'get_ServiceLocator_RuxHxcAService', true],
            'App\\Controller\\Admin\\OrderController::resendAccess' => ['privates', '.service_locator.ruxHxcA', 'get_ServiceLocator_RuxHxcAService', true],
            'App\\Controller\\Admin\\OrderController::show' => ['privates', '.service_locator.ruxHxcA', 'get_ServiceLocator_RuxHxcAService', true],
            'App\\Controller\\Admin\\VideoController::delete' => ['privates', '.service_locator.bki8G0J', 'get_ServiceLocator_Bki8G0JService', true],
            'App\\Controller\\Admin\\VideoController::edit' => ['privates', '.service_locator.bki8G0J', 'get_ServiceLocator_Bki8G0JService', true],
            'App\\Controller\\Admin\\VideoController::index' => ['privates', '.service_locator.Bd5JDSL', 'get_ServiceLocator_Bd5JDSLService', true],
            'App\\Controller\\Admin\\VideoController::show' => ['privates', '.service_locator.bki8G0J', 'get_ServiceLocator_Bki8G0JService', true],
            'App\\Controller\\Admin\\VideoController::toggleStatus' => ['privates', '.service_locator.bki8G0J', 'get_ServiceLocator_Bki8G0JService', true],
            'App\\Controller\\CheckoutController::captureOrder' => ['privates', '.service_locator.4n4ylFv', 'get_ServiceLocator_4n4ylFvService', true],
            'App\\Controller\\CheckoutController::success' => ['privates', '.service_locator.4n4ylFv', 'get_ServiceLocator_4n4ylFvService', true],
            'App\\Controller\\ContactController::index' => ['privates', '.service_locator.WZeIXfO', 'get_ServiceLocator_WZeIXfOService', true],
            'App\\Controller\\ContactController::instructor' => ['privates', '.service_locator.CsMkqUa', 'get_ServiceLocator_CsMkqUaService', true],
            'App\\Controller\\ContactController::message' => ['privates', '.service_locator.WZeIXfO', 'get_ServiceLocator_WZeIXfOService', true],
            'App\\Controller\\ContactController::registration' => ['privates', '.service_locator.CsMkqUa', 'get_ServiceLocator_CsMkqUaService', true],
            'App\\Controller\\CourseController::addToCart' => ['privates', '.service_locator.6lJWFv4', 'get_ServiceLocator_6lJWFv4Service', true],
            'App\\Controller\\CourseController::byMode' => ['privates', '.service_locator.QkVYtxV', 'get_ServiceLocator_QkVYtxVService', true],
            'App\\Controller\\CourseController::capitalManagement' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController::dayTrading' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController::enroll' => ['privates', '.service_locator.CLe7xTy', 'get_ServiceLocator_CLe7xTyService', true],
            'App\\Controller\\CourseController::financialMarkets' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController::fundamentalAnalysis' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController::list' => ['privates', '.service_locator.QkVYtxV', 'get_ServiceLocator_QkVYtxVService', true],
            'App\\Controller\\CourseController::myCourses' => ['privates', '.service_locator.Xf3Ro8W', 'get_ServiceLocator_Xf3Ro8WService', true],
            'App\\Controller\\CourseController::professionalTrader' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController::psychologicalAnalysis' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController::riskManagement' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController::show' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController::showModule' => ['privates', '.service_locator.ravAt47', 'get_ServiceLocator_RavAt47Service', true],
            'App\\Controller\\CourseController::technicalAnalysis' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController::tradingStrategies' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\GoogleAuthController::connectAction' => ['privates', '.service_locator.hokdgZZ', 'get_ServiceLocator_HokdgZZService', true],
            'App\\Controller\\GoogleAuthController::connectCheckAction' => ['privates', '.service_locator.hokdgZZ', 'get_ServiceLocator_HokdgZZService', true],
            'App\\Controller\\HomeController::about' => ['privates', '.service_locator.QkVYtxV', 'get_ServiceLocator_QkVYtxVService', true],
            'App\\Controller\\HomeController::index' => ['privates', '.service_locator.LKvMsLF', 'get_ServiceLocator_LKvMsLFService', true],
            'App\\Controller\\HomeController::instructors' => ['privates', '.service_locator.L29W5HS', 'get_ServiceLocator_L29W5HSService', true],
            'App\\Controller\\MarketAnalysisController::showBySeoSlug' => ['privates', '.service_locator.CsMkqUa', 'get_ServiceLocator_CsMkqUaService', true],
            'App\\Controller\\PasswordResetController::resetPassword' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\PasswordResetController::resetPasswordMerged' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\PaymentController::enrollInCourse' => ['privates', '.service_locator.QkVYtxV', 'get_ServiceLocator_QkVYtxVService', true],
            'App\\Controller\\SecurityController::login' => ['privates', '.service_locator.rSTd.nA', 'get_ServiceLocator_RSTd_NAService', true],
            'App\\Controller\\SecurityController::register' => ['privates', '.service_locator.1IiHida', 'get_ServiceLocator_1IiHidaService', true],
            'App\\Controller\\VideoController::list' => ['privates', '.service_locator.Bd5JDSL', 'get_ServiceLocator_Bd5JDSLService', true],
            'App\\Controller\\VideoController::show' => ['privates', '.service_locator.jezs8TR', 'get_ServiceLocator_Jezs8TRService', true],
            'App\\Kernel::loadRoutes' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'App\\Kernel::registerContainerConfiguration' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'kernel::loadRoutes' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'kernel::registerContainerConfiguration' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'App\\Controller\\AdminController:addAdmin' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AdminController:blockEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:blockUser' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController:certifyEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:createEnrollment' => ['privates', '.service_locator.ubULoiL', 'get_ServiceLocator_UbULoiLService', true],
            'App\\Controller\\AdminController:dashboard' => ['privates', '.service_locator.e5Iizj.', 'get_ServiceLocator_E5Iizj_Service', true],
            'App\\Controller\\AdminController:deleteAdmin' => ['privates', '.service_locator.Qyz7DBH', 'get_ServiceLocator_Qyz7DBHService', true],
            'App\\Controller\\AdminController:deleteCourse' => ['privates', '.service_locator.jqHm011', 'get_ServiceLocator_JqHm011Service', true],
            'App\\Controller\\AdminController:deleteEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:deletePartner' => ['privates', '.service_locator.YuEbyXL', 'get_ServiceLocator_YuEbyXLService', true],
            'App\\Controller\\AdminController:deletePromotionalBanner' => ['privates', '.service_locator.kleoBdA', 'get_ServiceLocator_KleoBdAService', true],
            'App\\Controller\\AdminController:deleteUser' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController:editAdmin' => ['privates', '.service_locator.mCbvPMW', 'get_ServiceLocator_MCbvPMWService', true],
            'App\\Controller\\AdminController:editEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:editPartner' => ['privates', '.service_locator.YuEbyXL', 'get_ServiceLocator_YuEbyXLService', true],
            'App\\Controller\\AdminController:editPromotionalBanner' => ['privates', '.service_locator.kleoBdA', 'get_ServiceLocator_KleoBdAService', true],
            'App\\Controller\\AdminController:enrollmentDetails' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:enrollmentDetailsByCode' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:enrollmentsList' => ['privates', '.service_locator.e5Iizj.', 'get_ServiceLocator_E5Iizj_Service', true],
            'App\\Controller\\AdminController:listAdmins' => ['privates', '.service_locator.Qyz7DBH', 'get_ServiceLocator_Qyz7DBHService', true],
            'App\\Controller\\AdminController:markEnrollmentCompleted' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:profile' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AdminController:showPartner' => ['privates', '.service_locator.YuEbyXL', 'get_ServiceLocator_YuEbyXLService', true],
            'App\\Controller\\AdminController:testEmail' => ['privates', '.service_locator.uVvF4gL', 'get_ServiceLocator_UVvF4gLService', true],
            'App\\Controller\\AdminController:toggleAdminStatus' => ['privates', '.service_locator.Qyz7DBH', 'get_ServiceLocator_Qyz7DBHService', true],
            'App\\Controller\\AdminController:toggleCourseStatus' => ['privates', '.service_locator.jqHm011', 'get_ServiceLocator_JqHm011Service', true],
            'App\\Controller\\AdminController:togglePartner' => ['privates', '.service_locator.YuEbyXL', 'get_ServiceLocator_YuEbyXLService', true],
            'App\\Controller\\AdminController:togglePromotionalBannerStatus' => ['privates', '.service_locator.kleoBdA', 'get_ServiceLocator_KleoBdAService', true],
            'App\\Controller\\AdminController:toggleUserStatus' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController:unblockEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:unblockUser' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController:updateEnrollment' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:updateEnrollmentProgress' => ['privates', '.service_locator.ewbgDlJ', 'get_ServiceLocator_EwbgDlJService', true],
            'App\\Controller\\AdminController:userEdit' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController:userShow' => ['privates', '.service_locator.Hz5btge', 'get_ServiceLocator_Hz5btgeService', true],
            'App\\Controller\\AdminController:viewAdmin' => ['privates', '.service_locator.mCbvPMW', 'get_ServiceLocator_MCbvPMWService', true],
            'App\\Controller\\AdminInstructorController:delete' => ['privates', '.service_locator.8RUuq9s', 'get_ServiceLocator_8RUuq9sService', true],
            'App\\Controller\\AdminInstructorController:edit' => ['privates', '.service_locator.c34Bris', 'get_ServiceLocator_C34BrisService', true],
            'App\\Controller\\AdminInstructorController:index' => ['privates', '.service_locator.L29W5HS', 'get_ServiceLocator_L29W5HSService', true],
            'App\\Controller\\AdminInstructorController:new' => ['privates', '.service_locator.c34Bris', 'get_ServiceLocator_C34BrisService', true],
            'App\\Controller\\AdminInstructorController:print' => ['privates', '.service_locator.4qavBNK', 'get_ServiceLocator_4qavBNKService', true],
            'App\\Controller\\AdminInstructorController:reorder' => ['privates', '.service_locator.L29W5HS', 'get_ServiceLocator_L29W5HSService', true],
            'App\\Controller\\AdminInstructorController:show' => ['privates', '.service_locator.L29W5HS', 'get_ServiceLocator_L29W5HSService', true],
            'App\\Controller\\AdminInstructorController:toggleStatus' => ['privates', '.service_locator.8RUuq9s', 'get_ServiceLocator_8RUuq9sService', true],
            'App\\Controller\\AdminMarketAnalysisController:delete' => ['privates', '.service_locator.WGDSHn2', 'get_ServiceLocator_WGDSHn2Service', true],
            'App\\Controller\\AdminMarketAnalysisController:edit' => ['privates', '.service_locator.WGDSHn2', 'get_ServiceLocator_WGDSHn2Service', true],
            'App\\Controller\\AdminMarketAnalysisController:show' => ['privates', '.service_locator.WGDSHn2', 'get_ServiceLocator_WGDSHn2Service', true],
            'App\\Controller\\AdminMarketAnalysisController:toggleStatus' => ['privates', '.service_locator.WGDSHn2', 'get_ServiceLocator_WGDSHn2Service', true],
            'App\\Controller\\AdminSecurityController:createAdmin' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AdminSecurityController:login' => ['privates', '.service_locator.rSTd.nA', 'get_ServiceLocator_RSTd_NAService', true],
            'App\\Controller\\Admin\\CategoryController:show' => ['privates', '.service_locator.8IESKP1', 'get_ServiceLocator_8IESKP1Service', true],
            'App\\Controller\\Admin\\CategoryController:toggleCourses' => ['privates', '.service_locator.8IESKP1', 'get_ServiceLocator_8IESKP1Service', true],
            'App\\Controller\\Admin\\CategoryController:toggleStatus' => ['privates', '.service_locator.8IESKP1', 'get_ServiceLocator_8IESKP1Service', true],
            'App\\Controller\\Admin\\CategoryController:toggleVideos' => ['privates', '.service_locator.8IESKP1', 'get_ServiceLocator_8IESKP1Service', true],
            'App\\Controller\\Admin\\DashboardController:index' => ['privates', '.service_locator..XI94lU', 'get_ServiceLocator__XI94lUService', true],
            'App\\Controller\\Admin\\OrderController:export' => ['privates', '.service_locator.4n4ylFv', 'get_ServiceLocator_4n4ylFvService', true],
            'App\\Controller\\Admin\\OrderController:index' => ['privates', '.service_locator.4n4ylFv', 'get_ServiceLocator_4n4ylFvService', true],
            'App\\Controller\\Admin\\OrderController:refund' => ['privates', '.service_locator.ruxHxcA', 'get_ServiceLocator_RuxHxcAService', true],
            'App\\Controller\\Admin\\OrderController:resendAccess' => ['privates', '.service_locator.ruxHxcA', 'get_ServiceLocator_RuxHxcAService', true],
            'App\\Controller\\Admin\\OrderController:show' => ['privates', '.service_locator.ruxHxcA', 'get_ServiceLocator_RuxHxcAService', true],
            'App\\Controller\\Admin\\VideoController:delete' => ['privates', '.service_locator.bki8G0J', 'get_ServiceLocator_Bki8G0JService', true],
            'App\\Controller\\Admin\\VideoController:edit' => ['privates', '.service_locator.bki8G0J', 'get_ServiceLocator_Bki8G0JService', true],
            'App\\Controller\\Admin\\VideoController:index' => ['privates', '.service_locator.Bd5JDSL', 'get_ServiceLocator_Bd5JDSLService', true],
            'App\\Controller\\Admin\\VideoController:show' => ['privates', '.service_locator.bki8G0J', 'get_ServiceLocator_Bki8G0JService', true],
            'App\\Controller\\Admin\\VideoController:toggleStatus' => ['privates', '.service_locator.bki8G0J', 'get_ServiceLocator_Bki8G0JService', true],
            'App\\Controller\\CheckoutController:captureOrder' => ['privates', '.service_locator.4n4ylFv', 'get_ServiceLocator_4n4ylFvService', true],
            'App\\Controller\\CheckoutController:success' => ['privates', '.service_locator.4n4ylFv', 'get_ServiceLocator_4n4ylFvService', true],
            'App\\Controller\\ContactController:index' => ['privates', '.service_locator.WZeIXfO', 'get_ServiceLocator_WZeIXfOService', true],
            'App\\Controller\\ContactController:instructor' => ['privates', '.service_locator.CsMkqUa', 'get_ServiceLocator_CsMkqUaService', true],
            'App\\Controller\\ContactController:message' => ['privates', '.service_locator.WZeIXfO', 'get_ServiceLocator_WZeIXfOService', true],
            'App\\Controller\\ContactController:registration' => ['privates', '.service_locator.CsMkqUa', 'get_ServiceLocator_CsMkqUaService', true],
            'App\\Controller\\CourseController:addToCart' => ['privates', '.service_locator.6lJWFv4', 'get_ServiceLocator_6lJWFv4Service', true],
            'App\\Controller\\CourseController:byMode' => ['privates', '.service_locator.QkVYtxV', 'get_ServiceLocator_QkVYtxVService', true],
            'App\\Controller\\CourseController:capitalManagement' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController:dayTrading' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController:enroll' => ['privates', '.service_locator.CLe7xTy', 'get_ServiceLocator_CLe7xTyService', true],
            'App\\Controller\\CourseController:financialMarkets' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController:fundamentalAnalysis' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController:list' => ['privates', '.service_locator.QkVYtxV', 'get_ServiceLocator_QkVYtxVService', true],
            'App\\Controller\\CourseController:myCourses' => ['privates', '.service_locator.Xf3Ro8W', 'get_ServiceLocator_Xf3Ro8WService', true],
            'App\\Controller\\CourseController:professionalTrader' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController:psychologicalAnalysis' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController:riskManagement' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController:show' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController:showModule' => ['privates', '.service_locator.ravAt47', 'get_ServiceLocator_RavAt47Service', true],
            'App\\Controller\\CourseController:technicalAnalysis' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\CourseController:tradingStrategies' => ['privates', '.service_locator.ZdZvBz9', 'get_ServiceLocator_ZdZvBz9Service', true],
            'App\\Controller\\GoogleAuthController:connectAction' => ['privates', '.service_locator.hokdgZZ', 'get_ServiceLocator_HokdgZZService', true],
            'App\\Controller\\GoogleAuthController:connectCheckAction' => ['privates', '.service_locator.hokdgZZ', 'get_ServiceLocator_HokdgZZService', true],
            'App\\Controller\\HomeController:about' => ['privates', '.service_locator.QkVYtxV', 'get_ServiceLocator_QkVYtxVService', true],
            'App\\Controller\\HomeController:index' => ['privates', '.service_locator.LKvMsLF', 'get_ServiceLocator_LKvMsLFService', true],
            'App\\Controller\\HomeController:instructors' => ['privates', '.service_locator.L29W5HS', 'get_ServiceLocator_L29W5HSService', true],
            'App\\Controller\\MarketAnalysisController:showBySeoSlug' => ['privates', '.service_locator.CsMkqUa', 'get_ServiceLocator_CsMkqUaService', true],
            'App\\Controller\\PasswordResetController:resetPassword' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\PasswordResetController:resetPasswordMerged' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\PaymentController:enrollInCourse' => ['privates', '.service_locator.QkVYtxV', 'get_ServiceLocator_QkVYtxVService', true],
            'App\\Controller\\SecurityController:login' => ['privates', '.service_locator.rSTd.nA', 'get_ServiceLocator_RSTd_NAService', true],
            'App\\Controller\\SecurityController:register' => ['privates', '.service_locator.1IiHida', 'get_ServiceLocator_1IiHidaService', true],
            'App\\Controller\\VideoController:list' => ['privates', '.service_locator.Bd5JDSL', 'get_ServiceLocator_Bd5JDSLService', true],
            'App\\Controller\\VideoController:show' => ['privates', '.service_locator.jezs8TR', 'get_ServiceLocator_Jezs8TRService', true],
            'kernel:loadRoutes' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'kernel:registerContainerConfiguration' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
        ], [
            'App\\Controller\\AdminController::addAdmin' => '?',
            'App\\Controller\\AdminController::blockEnrollment' => '?',
            'App\\Controller\\AdminController::blockUser' => '?',
            'App\\Controller\\AdminController::certifyEnrollment' => '?',
            'App\\Controller\\AdminController::createEnrollment' => '?',
            'App\\Controller\\AdminController::dashboard' => '?',
            'App\\Controller\\AdminController::deleteAdmin' => '?',
            'App\\Controller\\AdminController::deleteCourse' => '?',
            'App\\Controller\\AdminController::deleteEnrollment' => '?',
            'App\\Controller\\AdminController::deletePartner' => '?',
            'App\\Controller\\AdminController::deletePromotionalBanner' => '?',
            'App\\Controller\\AdminController::deleteUser' => '?',
            'App\\Controller\\AdminController::editAdmin' => '?',
            'App\\Controller\\AdminController::editEnrollment' => '?',
            'App\\Controller\\AdminController::editPartner' => '?',
            'App\\Controller\\AdminController::editPromotionalBanner' => '?',
            'App\\Controller\\AdminController::enrollmentDetails' => '?',
            'App\\Controller\\AdminController::enrollmentDetailsByCode' => '?',
            'App\\Controller\\AdminController::enrollmentsList' => '?',
            'App\\Controller\\AdminController::listAdmins' => '?',
            'App\\Controller\\AdminController::markEnrollmentCompleted' => '?',
            'App\\Controller\\AdminController::profile' => '?',
            'App\\Controller\\AdminController::showPartner' => '?',
            'App\\Controller\\AdminController::testEmail' => '?',
            'App\\Controller\\AdminController::toggleAdminStatus' => '?',
            'App\\Controller\\AdminController::toggleCourseStatus' => '?',
            'App\\Controller\\AdminController::togglePartner' => '?',
            'App\\Controller\\AdminController::togglePromotionalBannerStatus' => '?',
            'App\\Controller\\AdminController::toggleUserStatus' => '?',
            'App\\Controller\\AdminController::unblockEnrollment' => '?',
            'App\\Controller\\AdminController::unblockUser' => '?',
            'App\\Controller\\AdminController::updateEnrollment' => '?',
            'App\\Controller\\AdminController::updateEnrollmentProgress' => '?',
            'App\\Controller\\AdminController::userEdit' => '?',
            'App\\Controller\\AdminController::userShow' => '?',
            'App\\Controller\\AdminController::viewAdmin' => '?',
            'App\\Controller\\AdminInstructorController::delete' => '?',
            'App\\Controller\\AdminInstructorController::edit' => '?',
            'App\\Controller\\AdminInstructorController::index' => '?',
            'App\\Controller\\AdminInstructorController::new' => '?',
            'App\\Controller\\AdminInstructorController::print' => '?',
            'App\\Controller\\AdminInstructorController::reorder' => '?',
            'App\\Controller\\AdminInstructorController::show' => '?',
            'App\\Controller\\AdminInstructorController::toggleStatus' => '?',
            'App\\Controller\\AdminMarketAnalysisController::delete' => '?',
            'App\\Controller\\AdminMarketAnalysisController::edit' => '?',
            'App\\Controller\\AdminMarketAnalysisController::show' => '?',
            'App\\Controller\\AdminMarketAnalysisController::toggleStatus' => '?',
            'App\\Controller\\AdminSecurityController::createAdmin' => '?',
            'App\\Controller\\AdminSecurityController::login' => '?',
            'App\\Controller\\Admin\\CategoryController::show' => '?',
            'App\\Controller\\Admin\\CategoryController::toggleCourses' => '?',
            'App\\Controller\\Admin\\CategoryController::toggleStatus' => '?',
            'App\\Controller\\Admin\\CategoryController::toggleVideos' => '?',
            'App\\Controller\\Admin\\DashboardController::index' => '?',
            'App\\Controller\\Admin\\OrderController::export' => '?',
            'App\\Controller\\Admin\\OrderController::index' => '?',
            'App\\Controller\\Admin\\OrderController::refund' => '?',
            'App\\Controller\\Admin\\OrderController::resendAccess' => '?',
            'App\\Controller\\Admin\\OrderController::show' => '?',
            'App\\Controller\\Admin\\VideoController::delete' => '?',
            'App\\Controller\\Admin\\VideoController::edit' => '?',
            'App\\Controller\\Admin\\VideoController::index' => '?',
            'App\\Controller\\Admin\\VideoController::show' => '?',
            'App\\Controller\\Admin\\VideoController::toggleStatus' => '?',
            'App\\Controller\\CheckoutController::captureOrder' => '?',
            'App\\Controller\\CheckoutController::success' => '?',
            'App\\Controller\\ContactController::index' => '?',
            'App\\Controller\\ContactController::instructor' => '?',
            'App\\Controller\\ContactController::message' => '?',
            'App\\Controller\\ContactController::registration' => '?',
            'App\\Controller\\CourseController::addToCart' => '?',
            'App\\Controller\\CourseController::byMode' => '?',
            'App\\Controller\\CourseController::capitalManagement' => '?',
            'App\\Controller\\CourseController::dayTrading' => '?',
            'App\\Controller\\CourseController::enroll' => '?',
            'App\\Controller\\CourseController::financialMarkets' => '?',
            'App\\Controller\\CourseController::fundamentalAnalysis' => '?',
            'App\\Controller\\CourseController::list' => '?',
            'App\\Controller\\CourseController::myCourses' => '?',
            'App\\Controller\\CourseController::professionalTrader' => '?',
            'App\\Controller\\CourseController::psychologicalAnalysis' => '?',
            'App\\Controller\\CourseController::riskManagement' => '?',
            'App\\Controller\\CourseController::show' => '?',
            'App\\Controller\\CourseController::showModule' => '?',
            'App\\Controller\\CourseController::technicalAnalysis' => '?',
            'App\\Controller\\CourseController::tradingStrategies' => '?',
            'App\\Controller\\GoogleAuthController::connectAction' => '?',
            'App\\Controller\\GoogleAuthController::connectCheckAction' => '?',
            'App\\Controller\\HomeController::about' => '?',
            'App\\Controller\\HomeController::index' => '?',
            'App\\Controller\\HomeController::instructors' => '?',
            'App\\Controller\\MarketAnalysisController::showBySeoSlug' => '?',
            'App\\Controller\\PasswordResetController::resetPassword' => '?',
            'App\\Controller\\PasswordResetController::resetPasswordMerged' => '?',
            'App\\Controller\\PaymentController::enrollInCourse' => '?',
            'App\\Controller\\SecurityController::login' => '?',
            'App\\Controller\\SecurityController::register' => '?',
            'App\\Controller\\VideoController::list' => '?',
            'App\\Controller\\VideoController::show' => '?',
            'App\\Kernel::loadRoutes' => '?',
            'App\\Kernel::registerContainerConfiguration' => '?',
            'kernel::loadRoutes' => '?',
            'kernel::registerContainerConfiguration' => '?',
            'App\\Controller\\AdminController:addAdmin' => '?',
            'App\\Controller\\AdminController:blockEnrollment' => '?',
            'App\\Controller\\AdminController:blockUser' => '?',
            'App\\Controller\\AdminController:certifyEnrollment' => '?',
            'App\\Controller\\AdminController:createEnrollment' => '?',
            'App\\Controller\\AdminController:dashboard' => '?',
            'App\\Controller\\AdminController:deleteAdmin' => '?',
            'App\\Controller\\AdminController:deleteCourse' => '?',
            'App\\Controller\\AdminController:deleteEnrollment' => '?',
            'App\\Controller\\AdminController:deletePartner' => '?',
            'App\\Controller\\AdminController:deletePromotionalBanner' => '?',
            'App\\Controller\\AdminController:deleteUser' => '?',
            'App\\Controller\\AdminController:editAdmin' => '?',
            'App\\Controller\\AdminController:editEnrollment' => '?',
            'App\\Controller\\AdminController:editPartner' => '?',
            'App\\Controller\\AdminController:editPromotionalBanner' => '?',
            'App\\Controller\\AdminController:enrollmentDetails' => '?',
            'App\\Controller\\AdminController:enrollmentDetailsByCode' => '?',
            'App\\Controller\\AdminController:enrollmentsList' => '?',
            'App\\Controller\\AdminController:listAdmins' => '?',
            'App\\Controller\\AdminController:markEnrollmentCompleted' => '?',
            'App\\Controller\\AdminController:profile' => '?',
            'App\\Controller\\AdminController:showPartner' => '?',
            'App\\Controller\\AdminController:testEmail' => '?',
            'App\\Controller\\AdminController:toggleAdminStatus' => '?',
            'App\\Controller\\AdminController:toggleCourseStatus' => '?',
            'App\\Controller\\AdminController:togglePartner' => '?',
            'App\\Controller\\AdminController:togglePromotionalBannerStatus' => '?',
            'App\\Controller\\AdminController:toggleUserStatus' => '?',
            'App\\Controller\\AdminController:unblockEnrollment' => '?',
            'App\\Controller\\AdminController:unblockUser' => '?',
            'App\\Controller\\AdminController:updateEnrollment' => '?',
            'App\\Controller\\AdminController:updateEnrollmentProgress' => '?',
            'App\\Controller\\AdminController:userEdit' => '?',
            'App\\Controller\\AdminController:userShow' => '?',
            'App\\Controller\\AdminController:viewAdmin' => '?',
            'App\\Controller\\AdminInstructorController:delete' => '?',
            'App\\Controller\\AdminInstructorController:edit' => '?',
            'App\\Controller\\AdminInstructorController:index' => '?',
            'App\\Controller\\AdminInstructorController:new' => '?',
            'App\\Controller\\AdminInstructorController:print' => '?',
            'App\\Controller\\AdminInstructorController:reorder' => '?',
            'App\\Controller\\AdminInstructorController:show' => '?',
            'App\\Controller\\AdminInstructorController:toggleStatus' => '?',
            'App\\Controller\\AdminMarketAnalysisController:delete' => '?',
            'App\\Controller\\AdminMarketAnalysisController:edit' => '?',
            'App\\Controller\\AdminMarketAnalysisController:show' => '?',
            'App\\Controller\\AdminMarketAnalysisController:toggleStatus' => '?',
            'App\\Controller\\AdminSecurityController:createAdmin' => '?',
            'App\\Controller\\AdminSecurityController:login' => '?',
            'App\\Controller\\Admin\\CategoryController:show' => '?',
            'App\\Controller\\Admin\\CategoryController:toggleCourses' => '?',
            'App\\Controller\\Admin\\CategoryController:toggleStatus' => '?',
            'App\\Controller\\Admin\\CategoryController:toggleVideos' => '?',
            'App\\Controller\\Admin\\DashboardController:index' => '?',
            'App\\Controller\\Admin\\OrderController:export' => '?',
            'App\\Controller\\Admin\\OrderController:index' => '?',
            'App\\Controller\\Admin\\OrderController:refund' => '?',
            'App\\Controller\\Admin\\OrderController:resendAccess' => '?',
            'App\\Controller\\Admin\\OrderController:show' => '?',
            'App\\Controller\\Admin\\VideoController:delete' => '?',
            'App\\Controller\\Admin\\VideoController:edit' => '?',
            'App\\Controller\\Admin\\VideoController:index' => '?',
            'App\\Controller\\Admin\\VideoController:show' => '?',
            'App\\Controller\\Admin\\VideoController:toggleStatus' => '?',
            'App\\Controller\\CheckoutController:captureOrder' => '?',
            'App\\Controller\\CheckoutController:success' => '?',
            'App\\Controller\\ContactController:index' => '?',
            'App\\Controller\\ContactController:instructor' => '?',
            'App\\Controller\\ContactController:message' => '?',
            'App\\Controller\\ContactController:registration' => '?',
            'App\\Controller\\CourseController:addToCart' => '?',
            'App\\Controller\\CourseController:byMode' => '?',
            'App\\Controller\\CourseController:capitalManagement' => '?',
            'App\\Controller\\CourseController:dayTrading' => '?',
            'App\\Controller\\CourseController:enroll' => '?',
            'App\\Controller\\CourseController:financialMarkets' => '?',
            'App\\Controller\\CourseController:fundamentalAnalysis' => '?',
            'App\\Controller\\CourseController:list' => '?',
            'App\\Controller\\CourseController:myCourses' => '?',
            'App\\Controller\\CourseController:professionalTrader' => '?',
            'App\\Controller\\CourseController:psychologicalAnalysis' => '?',
            'App\\Controller\\CourseController:riskManagement' => '?',
            'App\\Controller\\CourseController:show' => '?',
            'App\\Controller\\CourseController:showModule' => '?',
            'App\\Controller\\CourseController:technicalAnalysis' => '?',
            'App\\Controller\\CourseController:tradingStrategies' => '?',
            'App\\Controller\\GoogleAuthController:connectAction' => '?',
            'App\\Controller\\GoogleAuthController:connectCheckAction' => '?',
            'App\\Controller\\HomeController:about' => '?',
            'App\\Controller\\HomeController:index' => '?',
            'App\\Controller\\HomeController:instructors' => '?',
            'App\\Controller\\MarketAnalysisController:showBySeoSlug' => '?',
            'App\\Controller\\PasswordResetController:resetPassword' => '?',
            'App\\Controller\\PasswordResetController:resetPasswordMerged' => '?',
            'App\\Controller\\PaymentController:enrollInCourse' => '?',
            'App\\Controller\\SecurityController:login' => '?',
            'App\\Controller\\SecurityController:register' => '?',
            'App\\Controller\\VideoController:list' => '?',
            'App\\Controller\\VideoController:show' => '?',
            'kernel:loadRoutes' => '?',
            'kernel:registerContainerConfiguration' => '?',
        ]);
    }
}
