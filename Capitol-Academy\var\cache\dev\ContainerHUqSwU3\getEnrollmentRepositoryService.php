<?php

namespace ContainerHUqSwU3;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getEnrollmentRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\EnrollmentRepository' shared autowired service.
     *
     * @return \App\Repository\EnrollmentRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'EnrollmentRepository.php';

        return $container->privates['App\\Repository\\EnrollmentRepository'] = new \App\Repository\EnrollmentRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
