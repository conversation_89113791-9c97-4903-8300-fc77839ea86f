<?php

namespace ContainerHUqSwU3;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getCertificationRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\CertificationRepository' shared autowired service.
     *
     * @return \App\Repository\CertificationRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'CertificationRepository.php';

        return $container->privates['App\\Repository\\CertificationRepository'] = new \App\Repository\CertificationRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
