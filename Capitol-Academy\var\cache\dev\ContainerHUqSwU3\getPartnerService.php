<?php

namespace ContainerHUqSwU3;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getPartnerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.YuEbyXL.App\Entity\Partner' shared service.
     *
     * @return \App\Entity\Partner
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.YuEbyXL": it needs an instance of "App\\Entity\\Partner" but this type has been excluded in "config/services.yaml".');
    }
}
