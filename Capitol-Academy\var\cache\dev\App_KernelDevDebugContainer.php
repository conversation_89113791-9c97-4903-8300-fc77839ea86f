<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerM6LDUix\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerM6LDUix/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerM6LDUix.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerM6LDUix\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerM6LDUix\App_KernelDevDebugContainer([
    'container.build_hash' => 'M6LDUix',
    'container.build_id' => 'fd6e03de',
    'container.build_time' => 1752424367,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerM6LDUix');
