<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerHUqSwU3\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerHUqSwU3/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerHUqSwU3.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerHUqSwU3\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerHUqSwU3\App_KernelDevDebugContainer([
    'container.build_hash' => 'HUqSwU3',
    'container.build_id' => '4107c82b',
    'container.build_time' => 1752422839,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerHUqSwU3');
